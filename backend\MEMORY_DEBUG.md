# Memory System Debug Guide

## Overview

The conversation memory system has been fixed and enhanced with better error handling, validation, and debugging capabilities. This guide explains how to test and debug the memory feature.

## What Was Fixed

### 1. **JSON Parsing Issues**
- Added robust JSON parsing with better error handling
- Implemented structure validation to ensure memory updates are valid
- Added fallback to empty memory structure when parsing fails

### 2. **Memory Update Logic**
- Fixed conditional logic that was preventing memory updates
- Added duplicate detection to avoid storing the same information multiple times
- Improved memory structure initialization

### 3. **Database Operations**
- Added proper error handling for database updates
- Only update database when there's actually new memory to store
- Better logging for debugging database operations

### 4. **Enhanced Logging**
- Added comprehensive logging with `[MEMORY]` prefix for easy filtering
- Detailed error messages and stack traces
- Progress tracking through the entire memory processing pipeline

### 5. **Debug Endpoints**
- Added debug endpoints to inspect memory state
- Added endpoint to force memory processing for testing
- Available only in development mode for security

## Debug Endpoints (Development Only)

### Get Memory Status
```
GET /api/debug/memory?user_id=USER_ID&companion_name=COMPANION_NAME
```

**Response:**
```json
{
  "success": true,
  "memoryInfo": {
    "hasMemory": true,
    "messageCount": 10,
    "lastMemoryProcessed": "2024-01-15T10:30:00.000Z",
    "memoryStats": {
      "totalConversations": 5,
      "relationshipDepth": 2.5
    },
    "personalDetails": {
      "interests": ["music", "guitar"],
      "relationships": ["sister Sarah"],
      "goals": [],
      "challenges": []
    },
    "establishedPatterns": {
      "nicknames": ["Ace"],
      "insideJokes": [],
      "conversationRituals": []
    }
  }
}
```

### Force Memory Processing
```
POST /api/debug/memory/process
Content-Type: application/json

{
  "user_id": "USER_ID",
  "companion_name": "COMPANION_NAME"
}
```

## Testing the Memory System

### Method 1: Using the Test Script

1. **Install axios** (if not already installed):
   ```bash
   npm install axios
   ```

2. **Run the test script**:
   ```bash
   cd backend
   node test-memory.js
   ```

   This will:
   - Send test messages with nicknames, interests, and personal information
   - Check memory state before and after each scenario
   - Force memory processing to ensure updates
   - Display a comprehensive memory summary

### Method 2: Manual Testing

1. **Start your server** in development mode:
   ```bash
   npm run dev
   ```

2. **Send some messages** through your chat interface or API:
   - Introduce yourself with a nickname: "Hi! I'm Alex, but call me Ace"
   - Share interests: "I love playing guitar and writing music"
   - Mention family: "My sister Sarah is studying medicine"

3. **Check memory status**:
   ```bash
   curl "http://localhost:8080/api/debug/memory?user_id=YOUR_USER_ID&companion_name=COMPANION_NAME"
   ```

4. **Force memory processing** if needed:
   ```bash
   curl -X POST http://localhost:8080/api/debug/memory/process \
     -H "Content-Type: application/json" \
     -d '{"user_id":"YOUR_USER_ID","companion_name":"COMPANION_NAME"}'
   ```

## Monitoring Memory Processing

### Console Logs to Watch For

**Successful Memory Processing:**
```
[AI_CONTROLLER] Triggering memory processing for user USER_ID and companion COMPANION_NAME
[MEMORY] Starting memory processing for USER_ID - COMPANION_NAME
[MEMORY] Processing 4 messages for memory extraction
[MEMORY] Raw OpenAI response: {"personalDetails":{"interests":["guitar"]...
[MEMORY] Successfully extracted memory: {...}
[MEMORY] Added new interests: guitar, music
[MEMORY] Added new nicknames: Ace
[MEMORY] Saving updated memory to database for chat CHAT_ID
[MEMORY] Successfully saved memory updates to database
[AI_CONTROLLER] Memory processing completed for user USER_ID and companion COMPANION_NAME
```

**Error Indicators:**
```
[MEMORY] Error extracting memory: SyntaxError: Unexpected token...
[MEMORY] JSON Parse Error - Raw content: ...
[MEMORY] No valid JSON object found in response
[MEMORY] Invalid memory structure returned
[MEMORY] Error saving memory to database: ...
```

## Common Issues and Solutions

### 1. **Memory Not Being Stored**
- Check console logs for JSON parsing errors
- Verify OpenAI API is responding correctly
- Use debug endpoint to check if memory processing is being triggered

### 2. **Duplicate Memory Entries**
- The system now prevents duplicates automatically
- If you see duplicates, check the deduplication logic in `updateConversationMemory`

### 3. **Memory Processing Not Triggered**
- Ensure `user_id` and companion `name` are provided in API calls
- Check that messages are being saved to chat history first
- Verify the async memory processing isn't failing silently

### 4. **OpenAI API Issues**
- Check your OpenAI API key and quota
- Monitor the raw OpenAI responses in console logs
- Verify the model specified in config is available

## Database Structure

The memory is stored in the `conversationMemory` field of the `ChatHistory` collection:

```javascript
{
  conversationMemory: {
    longTermMemory: {
      userProfile: {
        personalDetails: {
          interests: ["guitar", "music"],
          relationships: ["sister Sarah"],
          goals: [],
          challenges: []
        }
      },
      establishedPatterns: {
        nicknames: ["Ace"],
        insideJokes: [],
        conversationRituals: []
      },
      relationshipMilestones: [],
      emotionalHistory: []
    },
    memoryStats: {
      totalConversations: 5,
      relationshipDepth: 2.5,
      lastMemoryUpdate: "2024-01-15T10:30:00.000Z"
    }
  },
  lastMemoryProcessed: "2024-01-15T10:30:00.000Z"
}
```

## Next Steps

1. **Test the nickname scenario** that was failing before
2. **Monitor console logs** during testing to see the detailed processing flow
3. **Use debug endpoints** to verify memory is being stored correctly
4. **Check database directly** if needed to confirm memory persistence

The memory system should now work reliably and provide detailed feedback about what's happening during processing.
