/**
 * Memory System Test Script
 * Use this to test the memory feature manually
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:8080/api';
const TEST_USER_ID = 'test_user_123';
const TEST_COMPANION_NAME = 'Nova';

// Test scenarios
const testScenarios = [
  {
    name: "Nickname Introduction",
    messages: [
      "<PERSON> <PERSON>! My name is <PERSON>, but you can call me <PERSON>.",
      "That's a cool nickname! I'll remember to call you <PERSON> from now on."
    ]
  },
  {
    name: "Personal Interest",
    messages: [
      "I love playing guitar and writing music in my free time.",
      "That's amazing! Music is such a beautiful form of expression."
    ]
  },
  {
    name: "Family Information",
    messages: [
      "I have a younger sister named <PERSON> who's studying medicine.",
      "That's wonderful! It must be exciting to have a future doctor in the family."
    ]
  },
  {
    name: "Test Relationship Memory Fix",
    messages: [
      "My brother <PERSON> works as a software engineer at Google.",
      "That's impressive! Having a tech-savvy brother must be helpful."
    ]
  }
];

/**
 * Send a message to the AI and get response
 */
async function sendMessage(message, companionName = TEST_COMPANION_NAME, userId = TEST_USER_ID) {
  try {
    const response = await axios.post(`${BASE_URL}/get_ai_response`, {
      message: message,
      name: companionName,
      personality: "friendly",
      image: "emma.jpg",
      user_id: userId
    });

    return response.data;
  } catch (error) {
    console.error('Error sending message:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Get memory debug information
 */
async function getMemoryDebug(userId = TEST_USER_ID, companionName = TEST_COMPANION_NAME) {
  try {
    const response = await axios.get(`${BASE_URL}/debug/memory`, {
      params: {
        user_id: userId,
        companion_name: companionName
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting memory debug:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Force memory processing
 */
async function forceMemoryProcessing(userId = TEST_USER_ID, companionName = TEST_COMPANION_NAME) {
  try {
    const response = await axios.post(`${BASE_URL}/debug/memory/process`, {
      user_id: userId,
      companion_name: companionName
    });

    return response.data;
  } catch (error) {
    console.error('Error forcing memory processing:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Run a test scenario
 */
async function runTestScenario(scenario) {
  console.log(`\n=== Testing: ${scenario.name} ===`);

  for (let i = 0; i < scenario.messages.length; i++) {
    const message = scenario.messages[i];
    const isUserMessage = i % 2 === 0; // Even indices are user messages

    if (isUserMessage) {
      console.log(`\nUser: ${message}`);
      const response = await sendMessage(message);

      if (response && response.success) {
        console.log(`AI: ${response.llm_ans[0]}`);

        // Wait a bit for memory processing
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        console.log('Failed to get AI response');
        return false;
      }
    }
  }

  return true;
}

/**
 * Main test function
 */
async function runMemoryTests() {
  console.log('🧠 Memory System Test Started');
  console.log(`Testing with User ID: ${TEST_USER_ID}`);
  console.log(`Testing with Companion: ${TEST_COMPANION_NAME}`);

  // Check initial memory state
  console.log('\n📊 Checking initial memory state...');
  let memoryDebug = await getMemoryDebug();
  if (memoryDebug) {
    console.log('Initial memory state:', JSON.stringify(memoryDebug.memoryInfo, null, 2));
  }

  // Run test scenarios
  for (const scenario of testScenarios) {
    const success = await runTestScenario(scenario);
    if (!success) {
      console.log(`❌ Test scenario "${scenario.name}" failed`);
      continue;
    }

    // Force memory processing to ensure it's updated
    console.log('\n🔄 Forcing memory processing...');
    const forceResult = await forceMemoryProcessing();
    if (forceResult) {
      console.log('Force processing result:', forceResult.message || forceResult.error);
    }

    // Check memory state after scenario
    console.log('\n📊 Checking memory state after scenario...');
    memoryDebug = await getMemoryDebug();
    if (memoryDebug) {
      console.log('Updated memory state:', JSON.stringify(memoryDebug.memoryInfo, null, 2));
    }

    // Wait between scenarios
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Final memory check
  console.log('\n📊 Final memory state check...');
  memoryDebug = await getMemoryDebug();
  if (memoryDebug && memoryDebug.memoryInfo) {
    const memory = memoryDebug.memoryInfo;

    console.log('\n=== MEMORY SUMMARY ===');
    console.log(`Messages processed: ${memory.messageCount}`);
    console.log(`Has memory: ${memory.hasMemory}`);
    console.log(`Last processed: ${memory.lastMemoryProcessed}`);

    if (memory.personalDetails) {
      console.log('\nPersonal Details:');
      console.log(`- Interests: ${memory.personalDetails.interests?.join(', ') || 'None'}`);
      console.log(`- Relationships: ${memory.personalDetails.relationships?.join(', ') || 'None'}`);
      console.log(`- Goals: ${memory.personalDetails.goals?.join(', ') || 'None'}`);
      console.log(`- Challenges: ${memory.personalDetails.challenges?.join(', ') || 'None'}`);
    }

    if (memory.establishedPatterns) {
      console.log('\nEstablished Patterns:');
      console.log(`- Nicknames: ${memory.establishedPatterns.nicknames?.join(', ') || 'None'}`);
      console.log(`- Inside Jokes: ${memory.establishedPatterns.insideJokes?.join(', ') || 'None'}`);
      console.log(`- Rituals: ${memory.establishedPatterns.conversationRituals?.join(', ') || 'None'}`);
    }

    if (memory.memoryStats) {
      console.log('\nMemory Stats:');
      console.log(`- Total conversations: ${memory.memoryStats.totalConversations || 0}`);
      console.log(`- Relationship depth: ${memory.memoryStats.relationshipDepth || 1}/10`);
    }
  }

  console.log('\n✅ Memory System Test Completed');
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runMemoryTests().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  sendMessage,
  getMemoryDebug,
  forceMemoryProcessing,
  runTestScenario,
  runMemoryTests
};
